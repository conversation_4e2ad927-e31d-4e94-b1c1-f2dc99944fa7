// .__dropdown-wrap-mask {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100vh;
//   z-index: 20;
//   // background-color: rgba(0, 0, 0, 0.1);

.__dropdown-wrap {
  position: fixed;
  top: 0;
  left: 0;
  // width: 20rem;
  padding: 1rem 0;
  z-index: 20;
  border: 0.05rem solid #cccccc;
  border-radius: 0.25rem;
  background-color: #ffffff;
  box-shadow: 1px 1px 0.6rem 0 rgba(0, 0, 0, 0.2);
  max-height: 20rem;
  overflow-y: auto;

  .__dropdown-item {
    font-size: 1rem;
    padding: 0.5rem;
    cursor: pointer;
    box-sizing: border-box;
    color: #101010;
    transition: 0.2s;

    &:hover,
    &.focus-within {
      background-color: #f9e5e5;
    }

    &-active {
      background-color: #f9e5e5;
    }
  }
}

// }
