/**
 * @param {Array<Record<string, any>>} selectList 下拉列表项目
 * @param {Object} option {labelFiled: 'label', valueFiled: 'id', differentiation: {targets: Array<any>, classNames: Array<string>}}
 * @param option
 * -> labelFiled: 用于显示的字段
 * -> valueFiled: 用于传输的字段
 * -> differentiation: 同一项会进行额外的配置
 * -> targets: 需要额外配置的valueFiled
 * -> classNames: 需要的类数组
 */
HTMLInputElement.prototype.openDropDownSelect = function (
  selectList,
  option = {}
) {
  let popperRef = null

  option = Object.assign(
    {
      labelFiled: 'label',
      valueFiled: 'id',
      defaultItem: null,
      differentiation: {
        targets: [],
        classNames: [],
      },
    },
    option
  )

  if (option.defaultItem !== null && this.value) {
    const defaultItem = selectList.find(
      (item) => item[option.valueFiled] === option.defaultItem
    )
    this.value = defaultItem[option.labelFiled]
    this.dataset.valueFiled = defaultItem[option.valueFiled]
  }

  const inputValve = this.value
  let tabIndex = this.getAttribute('tabIndex') || '200'
  tabIndex = parseInt(tabIndex) + 1

  const maskRef = document.createElement('div')
  maskRef.classList.add('_dropdown-wrap')

  const chooseOption = (optionIndex) => {
    this.value = selectList[optionIndex].label
  }

  const onKeydown = (event, optionIndex) => {
    if (event.key === 'Enter') {
      chooseOption(optionIndex)
      if (maskRef) MaskRef.click()
    }
  }

  const closeDropdown = () => {
    if (popperRef) {
      popperRef.destroy()
      popperRef = null
    }
    maskRef.remove()
    this.removeEventListener('blur', closeDropdown)
  }

  maskRef.addEventListener('click', closeDropdown)

  const dropDownWrapDom = document.createElement('div')
  dropDownWrapDom.classList.add('__dropdown-wrap')
  dropDownWrapDom.style.width = `${this.getBoundingClientRect().width}px`

  const selectListDom = selectList.map((item, index) => {
    const isDifferentiation = option.differentiation.targets.includes(
      item[option.valueFiled]
    )
    let className = `__dropdown-item ${
      item[option.labelFiled] === inputValve ? '__dropdown-item-active' : ''
    }`
    if (isDifferentiation)
      className += ` ${option.differentiation.classNames.join(' ')}`
    return `<div class="${className}" tabindex="${tabIndex++}" data-value="${
      item[option.valueFiled]
    }">${item[option.labelFiled]}</div>`
  })

  dropDownWrapDom.insertAdjacentHTML('afterbegin', selectListDom.join(''))
  maskRef.append(dropDownWrapDom)
  document.body.append(maskRef)
  maskRef.addEventListener('focus', maskRef.click)

  dropDownWrapDom
    .querySelectorAll('.__dropdown-item')
    .forEach((item, index) => {
      item.addEventListener('click', (event) => {
        this.value = selectList[index][option.labelFiled]
        this.dataset.valueFiled = selectList[index][option.valueFiled]
        $(this).change()
        maskRef.click()
      })
      item.addEventListener('keydown', (event) => onKeydown(event, index))
    })

  this.addEventListener('blur', () => setTimeout(() => closeDropdown(), 200))

  popperRef = Popper.createPopper(this, dropDownWrapDom, {
    placement: 'bottom',
    modifiers: [
      {
        name: 'offset',
        options: {
          offset: [0, 8],
        },
      },
    ],
  })
}
