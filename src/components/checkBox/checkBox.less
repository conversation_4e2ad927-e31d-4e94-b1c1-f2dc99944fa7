// checkbox
.checkbox-group {
  cursor: pointer;

  .checkbox {
    position: relative;
    width: 0.9rem;
    height: 0.9rem;
    margin-right: 0.5rem;

    input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      filter: Alpha(opacity=0);
    }

    .icon-zh {
      float: left;
      // margin-top: .15rem;
      width: 1rem;
      height: 1rem;
      line-height: 0.85rem;
      font-size: 0.8rem;
      border: 2px solid @gray-lighter;
      border-radius: 0.2rem;
      text-align: center;

      &.focus-style {
        outline: 2px solid @gray-dark;
      }
    }

    .icon-zh-success-yuan:before {
      content: '';
    }
  }

  &.checked {
    .checkbox {
      .icon-zh-success-yuan {
        color: @picker-color-010;

        &:before {
          content: '\e613';
        }
      }
    }
  }

  &.disabled {
    .checkbox {
      .icon-zh {
        background: @gray-lightest;
        border-color: @gray-lightest;
      }
    }
  }
}
