/**
 * HX: Checkbox v1.0.0 通用多选框
 * Author: dyy
 * =================================
 * Copyright 2024- Travelsky ICED
 * response Checkbox component
 */

;+(function ($) {
  // checkbox 点击事件
  $('.checkbox-group input[type="checkbox"]').on('click', function () {
    var is_check = $(this).is(':checked')
    var par = $(this).parents('.checkbox-group')
    // 选中状态
    if (is_check) {
      par.addClass('checked')
      par.attr('aria-checked', true)
      // 非选中状态
    } else {
      par.attr('aria-checked', false)
      par.removeClass('checked')
    }
  })

  // checkbox Tab focus事件
  $('.checkbox-group .checkbox input').focus(function () {
    $(this).parents('.checkbox-group').addClass('focus')
  })

  // checkbox Tab blur事件
  $('.checkbox-group .checkbox input').blur(function () {
    $(this).parents('.checkbox-group').removeClass('focus')
  })
})(jQuery)
