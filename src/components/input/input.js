/**
 * ZH: Input v1.0.0 通用输入框
 * Author: zijing
 * =================================
 * Copyright 2024- Travelsky ICED
 * response input component
 */

+(function($) {
  // 输入框聚焦事件
  $(document).on('focus', '.input-group input', function() {
    if ($(this).parents(".input-group").hasClass("disabled")) return;
    var val = $(this).val();
    var $par = $(this).parents(".input-group");
    $par.addClass("active");
    // 自动展开提示信息
    if ($par.find(".badge-info").length > 0) {
      $par.find(".badge-info").show();
    }

    // toggle 文本填充样式
    if (val.length > 0) {
      $par.addClass("filled");
    } else {
      $par.removeClass("filled");
    }
  });

  // 输入框失焦事件
  $(document).on('blur', '.input-group input', function() {
    var $par = $(this).parents(".input-group");
    $par.removeClass("active");
    // 自动收起提示信息
    if ($par.find(".badge-info").length > 0) {
      $par.find(".badge-info").hide();
    }
  });

  // 输入框输入事件
  $(document).on('input', '.input-group input', function() {
    var val = $(this).val();
    var $par = $(this).parents(".input-group");
    // toggle 文本填充样式
    if (val.length > 0) {
      $par.addClass("filled");
    } else {
      $par.removeClass("filled");
    }
  });

  // 输入框clean事件
  $(document).on('click', '.input-group .clean-txt', function() {
    var $par = $(this).parents(".input-group");
    var obj = $par.find("input");
    // 清空文本
    if (obj.length > 0) {
      obj.val("");
      // 清除填充样式
      $par.removeClass("filled");
    }
  });


  // 乘客选择框聚焦事件
  $('.input-passenger .text').focus(function() {
    var $inputWrap = $(this).parents('.input-passenger');
    var $this = $inputWrap.find('.passenger-count-wrap');
    $('.passenger-count-wrap').hide();
    if ($this.length) {
      $this.show();
    }
  });

  // 乘客选择框全局关闭事件
  $(document).on('click', function(e) {
    var _hasThis = $(e.target).hasClass('input-passenger');
    var _hasParents = $(e.target).parents('.input-passenger').length;
    var _has = _hasThis || _hasParents;
    if (!_has) {
      $('.passenger-count-wrap').hide();
    }
  });

  // 乘客类型切换事件
  $('.passenger-count-wrap .passenger-type').on('click', '.type', function() {
    var thisIndex = $(this).index();
    $(this).siblings().removeClass('active');
    $(this).addClass('active');
    $(this).closest('.passenger-count-wrap').find('.passenger-count-list').hide();
    $(this).closest('.passenger-count-wrap').find('.passenger-count-list').eq(thisIndex).show();
  });

  // 日历选择框全局关闭事件
  $('.calendar-input').click(function() {
    $('.passenger-count-wrap').hide();
  });


  // placeholder兼容
  function isUnderIE9() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
    var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
    var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1; //判断是否IE11浏览器
    if (isIE) {
      var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
      reIE.test(userAgent);
      var fIEVersion = parseFloat(RegExp["$1"]);
      if (fIEVersion == 7) {
        return true;
      } else if (fIEVersion == 8) {
        return true;
      } else if (fIEVersion == 9) {
        return true;
      } else if (fIEVersion == 10) {
        return true;
      } else {
        return false;
      }
    } else if (isEdge) {
      return false;
    } else if (isIE11) {
      return false;
    } else {
      return false;
    }
  }

  $('input').each(function() {
    if (isUnderIE9()) {
      var placeholder = $(this).attr('placeholder');
      var value = $(this).val();
      if (placeholder) {
        if (!value) {
          $(this).attr('value', placeholder);
          $(this).addClass('no-support');
        }
        $(this).focus(function() {
          var newValue = $(this).val();
          if (newValue === placeholder) {
            $(this).val('');
            $(this).removeClass('no-support');
          }
        });
        $(this).blur(function() {
          var $this = $(this);
          setTimeout(function() {
            var newValue = $this.val();
            if (!newValue) {
              $this.val(placeholder);
              $this.addClass('no-support');
            }
          }, 100);
        });
      }
    }
  });

})(jQuery);