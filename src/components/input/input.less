// inputs 组件
//
// input, padding, border and color for body text, size, and more.
.input-group {
  position: relative;
  width: 100%;
  font-size: 0.8rem;
  color: @gray-dark;
  border-radius: 0.2rem;

  .error-tips {
    display: none;
    font-size: 0.7rem;
    color: #ff404f;
  }

  .text {
    width: 100%;
    padding: 0.55rem 0.75rem;
    padding-right: 1.2rem;
    outline: none;
    border-radius: 0.25rem;
  }

  .l-info {
    display: inline-block;
    margin: 0.55rem 0;
    width: 1rem;
    padding-left: 0.75rem;
    padding-right: 2px solid @gray-83;
  }

  .r-text {
    width: -webkit-calc(100% - 4.1rem);
    width: calc(100% - 4.1rem);
    border: none;
    outline: none;
    padding: 0.55rem 0.75rem;
    padding-right: 1.2rem;
  }

  .text-entry {
    border: 1px solid @gray-83;
    border-radius: 0.25rem;
  }

  .clean-txt {
    display: none;
    position: absolute;
    right: 0.7rem;
    top: 0.75rem;
    width: 1.3rem;
    height: 1.3rem;
    color: @gray-dark;
    cursor: pointer;
    font-size: 1rem;
  }

  .badge-info {
    position: absolute;
    left: 0;
    top: 2.5rem;
    z-index: 99;
    display: none;
  }

  &.filled {
    .clean-txt {
      display: block;
      // display: none; //IE9使用自身效果
    }

    &.focus {
      .clean-txt {
        display: none; //IE9使用自身效果
      }
    }
    .text-entry {
      border-color: @primary-color;
    }
  }

  &.disabled {
    cursor: not-allowed;

    .text,
    .text-entry,
    label {
      color: #dbd7d4;
    }

    .icon-zh {
      color: #dbd7d4;
    }

    .text {
      &:-ms-input-placeholder {
        color: #dbd7d4 !important;
      }

      &::-moz-placeholder {
        color: #dbd7d4 !important;
      }

      &:-moz-placeholder {
        color: #dbd7d4 !important;
      }

      &::-webkit-input-placeholder {
        color: #dbd7d4 !important;
      }

      &::placeholder {
        color: #dbd7d4 !important;
      }
    }
  }

  &:not(.disabled):not(.error):hover {
    .text,
    .text-entry {
      border: 1px solid #4f3d1e;
    }
  }

  &:not(.disabled):not(.error).active {
    .text,
    .text-entry {
      border: 1px solid #4f3d1e;
    }
  }

  &.error {
    .text,
    .text-entry {
      border: 1px solid #ff404f;
      color: #ff404f;
    }

    &:not(.disabled):not(.disabled):hover {
      .text,
      .text-entry {
        border: 1px solid #ff404f !important;
      }
    }

    .error-tips {
      display: block;
      position: absolute;
      margin-top: 5px;

      @media (max-width: @screen-xs-max) {
        position: initial;
      }
    }
  }

  .text {
    padding-left: 9px;
    padding-right: 9px;
    border-radius: 4px;
    background: #fff;
    border: 1px solid #4f3d1e;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.08);

    // .bubble-info {
    //   left: -10px;
    //   right: -10px;
    // }
  }

  &.error {
    .text {
      padding-left: 9px;
      padding-right: 9px;
      border-radius: 4px;
      border: 1px solid #ff404f;
      box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.08);
      color: #ff404f;
    }
  }

  .label-wrap {
    .bubble-info {
      left: 10px;
    }
  }

  .input-single {
    padding-left: 0;
    position: relative;
    // font-family: @font-family-sans-semibold;

    .label-wrap {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      // width: 90%;
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    label {
      vertical-align: top;
      margin-bottom: 0;
      font-size: 14px;
      color: #4f3d1e;

      @media screen and (max-width: @screen-xs-max) {
        font-size: 14px;
      }
    }

    &.input-group {
      position: relative;

      .text {
        padding: 35px 9px 6px;
        height: 66px;
        border: 1px solid transparent;
        border-radius: 0;
        background: none;
        border-bottom-color: @gray-lighter;
        font-size: 1rem;
        color: @gray-101010;
        text-overflow: ellipsis;

        @media screen and (max-width: @screen-xs-max) {
          font-size: 0.8rem;
          height: 63px;
        }
      }

      &:-ms-input-placeholder {
        color: #4f3d1e !important;
      }

      &::-moz-placeholder {
        color: #4f3d1e !important;
      }

      &:-moz-placeholder {
        color: #4f3d1e !important;
      }

      &::-webkit-input-placeholder {
        color: #4f3d1e !important;
      }

      &::placeholder {
        color: #4f3d1e !important;
      }

      &.no-support {
        color: #4f3d1e;
      }
    }

    .no-border-bottom {
      border-bottom-color: transparent;
    }

    &:not(.disabled):not(.error).active,
    &:not(.disabled):not(.disabled):hover {
      .label-wrap {
        // left: 10px;
      }
    }

    .calendar {
      right: 10px;
    }
  }

  .clean-txt {
    right: 10px;
    top: 35px;
    width: auto;
    height: auto;
    line-height: 1;
    color: @gray-dark;
  }

  .icon-Not-Available1 {
    display: block;
  }

  .badge {
    padding: 0 10px;
    background: none;
    font-size: 14px;
  }

  .calendar {
    position: absolute;
    top: 33px;
    right: 10px;
    font-size: 0.8rem;
    color: #554e49;
    pointer-events: none;

    // @media (max-width: @screen-lg-max) {
    //   right: 0;
    // }
    @media (max-width: @screen-md-max) {
      right: 10px;
    }
  }

  .icon-zh-right-type1 {
    .calendar();
    transform: rotate(90deg);
  }

  .prompt-bubble-sigh {
    .prompt-btn {
      margin-left: 8px;
    }
  }
}
