// Options
//
// Quickly modify global styling by enabling or disabling optional features.

:root {
  --gutter-x: 1rem;
  --gutter-y: 1rem;
}

@enable-grid-classes: true;

//== Colors
//
//## Gray and brand colors for use across Bootstrap.

@primary-color: #cc0100;
@picker-color-010: #cc0100;
@picker-bg-color: darken(@primary-color, 11.37%); // #B10000
@primary-dark-color: hsl(340, 60%, 34%);

@asist-color: hsl(340, 60%, 34%);
@note-color: #f8f1f4;

@color-white: #fff;

//灰色系
@gray-base: #000;
@gray-dark: lighten(@gray-base, 20%); // #333
@gray-59: lighten(@gray-base, 33.5%); // #555
@gray-666: lighten(@gray-base, 35%); // #595959
@gray-75: lighten(@gray-base, 40%); // #666
@gray-light: lighten(@gray-base, 45.88%); // #757575
@gray-lighter: lighten(@gray-base, 60%); // #999
@gray-83: lighten(@gray-base, 83%); // #d3d3d3
@gray-lightest: lighten(@gray-base, 95%); // #f2f2f2
@gray-98: lighten(@gray-base, 98%); // #fafafa
@gray-97: #f7f7f7;
@gray-101010: #101010;
@gray-765: #7a6a5e;
@gray-29: #4b4b4b;
@gray-25: #252525;
@gray-2e: #2e2e2e;
@gray-15: #151515;
@gray-e1: #e1e1e1;
@gray-e5: #e5e5e5;
@gray-f7: #f7f7f7;
@gray-e8: #e8e8e8;
@gray-d4: #d4d4d4;
@gray-d2: #d2d2d2;
@gray-d74: #d8d7d4;

//紫色系
@purple-base: #0c0018;
@purple-07: rgba(12, 0, 27, 0.7);
@gray-fa: #fafafa; // #fafafa;
@purple-97: #972159;
@purple-59: #592e91;
@purple-8c: #8c2346;
@purple-a7: #a7004b;
@purple-74: #744aaa;
@purple-b5: #b52c59;
@purple-50: #501b86;
@purple-cb: #cb869d;

@banner-color: #d9272e;
@banner-color-09: rgba(217, 39, 46, 0.9);
@red-ed: #ed1f1f;
@red-b1: #b10000;
@red-9c: #9c3d4d;
@red-7a: #7a0000;

@green-19: #199000;
@yellow-ff: #ffb018;
@yellow-c2: #c24d00;

// Paragraphs
//
// Style p element.

@paragraph-margin-bottom: 0;

// 屏幕的间值
@screen-lg: 1440px;
@screen-md: 1025px;
@screen-xs: 768px;

// 列间距
@grid-gutter-width: 30px;

// 总的列数
@grid-columns: 12;

@screen-xs-max: 767px; // 767px;
@screen-lg-max: 1439px;
@screen-md-max: 1024px;

// stylelint-enable value-keyword-case
@font-size-base: 0.8rem; // Assumes the browser default, typically 16px
@font-size-lg: (@font-size-base * 1.25);
@font-size-sm: (@font-size-base * 0.875);
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-bold: 700;
@font-weight-base: @font-weight-normal;
@line-height-base: 1.5;
@font-family-sans-serif: 'OpenSans-Regular', PingFangSC-Regular,
  'Microsoft YaHei', 'Meiryo', Helvetica, 'Lato', 'Microsoft JhengHei';

@screen-mobile: 768px; // 移动端
@screen-pad: 1023px; // 平板
@screen-pc: 1440px; // 电脑

// ------------------------------------------------------------------------------------------------------------------
/* 品牌色 */
@brand-1: #cc0100;
@brand-2: #942531;
@brand-3: #942531;
@brand-4: #e64343;

/* 辅色 */
@sub-1: #f8f1e5;
@sub-1-30: #4df8f1e5;
@sub-2: #b4aba4;
@sub-3: #7a6a5e;
@sub-4: #4f3d1e;
@sub-5: #ebddba;

/* 中性色 */
@gray-0: #ffffff;
@gray-1_1: #f4f6f9;
@gray-1: #dbd7d4;
@gray-2: #cccccc;
@gray-3: #554e49;
@gray-4: #2c2420;
@gray-5: #101010;
@gray-6: #4c5560;

/* 状态色 */
@green-1: #edf9e8;
@green-2: #52c41a;
@red-1: #ffeded;
@red-2: #ff4d4f;
@orange-1: #fff3ed;
@orange-2: #ff8e4d;
@orange-3: #c47330;
@orange-linear: linear-gradient(270deg, #c47330 0%, #e18132 100%);
@yellow-1: #fdf7e8;
@yellow-2: #eeb71c;
