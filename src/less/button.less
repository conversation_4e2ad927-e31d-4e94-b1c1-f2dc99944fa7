/* 主按钮 */
.main-button {
  background: #cc0100;
  color: #fff;
  height: 2.6rem;
  line-height: 2.6rem;
  font-size: 1rem;
  text-align: center;
  transition: 0.2s;
  border-radius: 0.4rem;

  &:hover,
  &:active,
  &:focus {
    background: #942531;
  }

  &-disable {
    background-color: #dbd7d4;
    color: #b4b4b4;

    &:hover,
    &:active,
    &:focus {
      background-color: #dbd7d4;
      color: #b4b4b4;
      cursor: not-allowed;
    }
  }
}

/* 次按钮 */
.secondary-button {
  border: 0.05rem solid #cc0100;
  color: #cc0100;
  height: 2.6rem;
  line-height: 2.6rem;
  font-size: 1rem;
  text-align: center;
  transition: 0.2s;
  border-radius: 0.4rem;

  &:hover,
  &:active,
  &:focus {
    border-color: #942531;
    color: #942531;
  }

  &-disable {
    border-color: #dbd7d4;
    color: #b4b4b4;

    &:hover,
    &:active,
    &:focus {
      border-color: #dbd7d4;
      color: #b4b4b4;
      cursor: not-allowed;
    }
  }
}

/* 次按钮 2 */
.secondary-button-2 {
  background: #cc0100;
  color: #fff;
  height: 2rem;
  line-height: 2rem;
  font-size: 0.8rem;
  text-align: center;
  transition: 0.2s;
  border-radius: 0.4rem;
  cursor: pointer;
  position: relative;
  z-index: 2;

  &:hover,
  &:active,
  &:focus {
    background: #942531;
  }

  &-disable {
    background-color: #dbd7d4;
    color: #b4b4b4;

    &:hover,
    &:active,
    &:focus {
      background-color: #dbd7d4;
      cursor: not-allowed;
    }
  }
}

/* 文字按钮 */
.text-button {
  display: flex;
  align-items: center;

  &-label {
    color: #7a6a5e;
    font-size: 0.8rem;
    margin-right: 0.2rem;
    transition: 0.2s;
  }

  &-icon {
    width: 0.8rem;
    height: 0.8rem;
    object-fit: contain;
  }

  &:hover,
  &:active,
  &:focus {
    .text-button-label {
      color: #942531;
    }
  }
}
